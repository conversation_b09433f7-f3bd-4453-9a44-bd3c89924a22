#!/usr/bin/env python3
"""
Undetected ChromeDriver工具模块
专门用于绕过反爬虫检测的Chrome浏览器设置
"""

import logging
import random
import time
import os
from pathlib import Path
from typing import Optional

try:
    import undetected_chromedriver as uc
    UNDETECTED_AVAILABLE = True
except ImportError:
    UNDETECTED_AVAILABLE = False
    import warnings
    warnings.warn("undetected-chromedriver未安装，请运行: pip install undetected-chromedriver")

logger = logging.getLogger(__name__)

class UndetectedChromeManager:
    """Undetected ChromeDriver管理器"""
    
    def __init__(self):
        self.driver = None
        self.options = None
        
    def create_options(self, headless: bool = False, profile_path: str = None) -> 'uc.ChromeOptions':
        """创建优化的Chrome选项 - 进一步修复Chrome连接问题"""
        if not UNDETECTED_AVAILABLE:
            raise ImportError("undetected-chromedriver未安装")

        options = uc.ChromeOptions()

        # 基础设置 - 简化参数避免冲突
        if headless:
            options.add_argument('--headless=new')
        else:
            # 简化窗口设置，避免重复参数
            options.add_argument('--start-maximized')

        # 核心稳定性参数
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')  # 重新启用，有助于稳定性

        # 反检测设置 - 保持最小化
        options.add_argument('--disable-blink-features=AutomationControlled')

        # 网络和安全设置
        options.add_argument('--disable-web-security')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--allow-running-insecure-content')

        # 禁用可能导致问题的功能
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-plugins')
        options.add_argument('--disable-images')  # 加快加载速度
        options.add_argument('--disable-javascript')  # 暂时禁用JS，减少复杂性

        # 用户体验优化
        options.add_argument('--disable-notifications')
        options.add_argument('--disable-popup-blocking')
        options.add_argument('--disable-translate')
        options.add_argument('--disable-background-timer-throttling')
        options.add_argument('--disable-backgrounding-occluded-windows')
        options.add_argument('--disable-renderer-backgrounding')

        # 内存优化
        options.add_argument('--memory-pressure-off')
        options.add_argument('--max_old_space_size=4096')

        # 调试和日志设置
        options.add_argument('--enable-logging')
        options.add_argument('--log-level=0')
        options.add_argument('--v=1')

        # 设置用户数据目录
        if profile_path:
            profile_path = Path(profile_path)
            try:
                profile_path.mkdir(parents=True, exist_ok=True)
                # 确保路径格式正确
                profile_path_str = str(profile_path).replace('\\', '/')
                options.add_argument(f'--user-data-dir={profile_path_str}')
                logger.info(f"使用用户配置目录: {profile_path_str}")

                # 创建必要的子目录
                (profile_path / "Default").mkdir(exist_ok=True)
                logger.debug(f"确保配置目录结构完整")
            except Exception as e:
                logger.warning(f"设置配置目录时出错: {e}")

        # 简化用户代理设置
        options.add_argument('--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')

        # 最小化实验性选项
        prefs = {
            "profile.default_content_setting_values": {
                "notifications": 2,
                "geolocation": 2,
                "media_stream": 2,
            },
            "profile.password_manager_enabled": True,
            "credentials_enable_service": True,
        }
        options.add_experimental_option("prefs", prefs)

        # 添加Chrome特定的稳定性选项
        options.add_argument('--disable-features=VizDisplayCompositor,VizServiceDisplay')
        options.add_argument('--disable-ipc-flooding-protection')
        options.add_argument('--disable-hang-monitor')
        options.add_argument('--disable-client-side-phishing-detection')
        options.add_argument('--disable-component-update')
        options.add_argument('--disable-default-apps')
        options.add_argument('--disable-domain-reliability')

        self.options = options
        return options
    
    def create_driver(self,
                     headless: bool = False,
                     profile_path: str = None,
                     version_main: int = None,
                     driver_executable_path: str = None) -> 'uc.Chrome':
        """创建Undetected Chrome驱动 - 进一步改进连接稳定性"""
        if not UNDETECTED_AVAILABLE:
            raise ImportError("undetected-chromedriver未安装")

        # 注释掉初始清理，避免影响用户正在使用的浏览器
        # self._cleanup_chrome_processes()

        # 自动检测本地Chrome主版本号
        def get_local_chrome_major_version():
            try:
                import chromedriver_autoinstaller
                version = chromedriver_autoinstaller.utils.get_chrome_version()
                if version:
                    return int(version.split('.')[0])
            except Exception as e:
                logger.warning(f"自动检测本地Chrome主版本失败: {e}")

            # 备用检测方法
            try:
                import subprocess
                import re
                result = subprocess.run(['chrome', '--version'], capture_output=True, text=True, timeout=5)
                if result.returncode == 0:
                    match = re.search(r'Chrome/(\d+)', result.stdout)
                    if match:
                        return int(match.group(1))
            except:
                pass

            return None

        # 使用指定的Chrome版本或自动检测
        if version_main:
            chrome_major_version = version_main
            logger.info(f"使用指定的Chrome主版本: {chrome_major_version}")
        else:
            chrome_major_version = get_local_chrome_major_version()
            if chrome_major_version:
                logger.info(f"自动检测到Chrome主版本: {chrome_major_version}")
            else:
                logger.warning("无法检测Chrome版本，让undetected-chromedriver自动处理")

        # 多次尝试创建驱动，每次使用不同的策略
        max_attempts = 3
        for attempt in range(max_attempts):
            try:
                logger.info(f"尝试创建Undetected ChromeDriver (第{attempt+1}次尝试)")

                # 每次尝试都重新创建选项，避免状态污染
                options = self.create_options(headless=headless, profile_path=profile_path)

                # 基础驱动参数 - 简化配置
                driver_kwargs = {
                    'options': options,
                    'use_subprocess': False,  # 改为False，可能更稳定
                    'keep_alive': False,      # 改为False，避免连接问题
                }

                # 设置Chrome版本
                if chrome_major_version:
                    driver_kwargs['version_main'] = chrome_major_version
                    logger.info(f"设置ChromeDriver版本: {chrome_major_version}")
                else:
                    logger.info("让undetected-chromedriver自动检测版本")

                if driver_executable_path:
                    driver_kwargs['driver_executable_path'] = driver_executable_path
                    logger.info(f"使用指定驱动路径: {driver_executable_path}")

                # 🔧 在创建驱动前检查和清理端口
                self._ensure_clean_environment()

                # 创建驱动
                logger.debug(f"创建驱动参数: {driver_kwargs}")
                self.driver = uc.Chrome(**driver_kwargs)

                # 设置基本超时 - 为自动检测版本提供更多时间
                timeout_multiplier = 2 if attempt == 0 else 1  # 第一次尝试（自动检测）使用更长超时
                self.driver.implicitly_wait(5 * timeout_multiplier)
                self.driver.set_page_load_timeout(30 * timeout_multiplier)

                # 简单验证驱动是否正常工作
                try:
                    self.driver.get("data:text/html,<html><body>Test</body></html>")
                    logger.info(f"Undetected ChromeDriver创建成功 (第{attempt+1}次尝试)")

                    # 应用反检测脚本
                    self._apply_stealth_scripts()

                    return self.driver
                except Exception as e:
                    logger.warning(f"驱动验证失败: {e}")
                    raise e

            except Exception as e:
                error_msg = str(e)
                logger.warning(f"第{attempt+1}次创建Undetected ChromeDriver失败: {error_msg}")

                # 清理失败的驱动
                if hasattr(self, 'driver') and self.driver:
                    try:
                        self.driver.quit()
                    except:
                        pass
                    self.driver = None

                # 🔧 增强的错误处理
                if "cannot connect to chrome" in error_msg.lower():
                    logger.warning("Chrome连接失败，执行彻底清理后重试")
                    self._cleanup_chrome_processes()
                    self._cleanup_debug_ports()  # 清理端口占用
                    time.sleep(5)  # 增加等待时间
                elif "session not created" in error_msg.lower():
                    logger.warning("会话创建失败，清理环境后重试")
                    self._ensure_clean_environment()
                    time.sleep(3)
                elif "expecting ',' delimiter" in error_msg.lower() or "json" in error_msg.lower():
                    logger.warning("Chrome配置文件可能损坏，尝试清理配置文件")
                    if profile_path:
                        self._cleanup_chrome_profile(profile_path)
                    time.sleep(3)
                else:
                    # 其他未知错误，执行通用清理
                    logger.warning(f"未知Chrome错误，执行通用清理: {error_msg[:100]}")
                    self._ensure_clean_environment()
                    time.sleep(2)

                # 如果是最后一次尝试，抛出异常
                if attempt == max_attempts - 1:
                    raise RuntimeError(f"创建Undetected ChromeDriver失败 (尝试{max_attempts}次): {error_msg}")

                # 等待一下再重试
                time.sleep(1)

        # 理论上不会到达这里
        raise RuntimeError("创建Undetected ChromeDriver失败：未知错误")

    def _cleanup_chrome_processes(self):
        """增强的Chrome进程清理 - 更彻底的清理策略"""
        try:
            import psutil
            import subprocess
            import os

            cleaned_count = 0

            # 🔧 方法1: 使用psutil清理自动化Chrome进程
            for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
                try:
                    if 'chrome' in proc.info['name'].lower():
                        cmdline = ' '.join(proc.info.get('cmdline', []))

                        # 扩展自动化Chrome进程识别条件
                        automation_keywords = [
                            '--remote-debugging-port',  # 自动化Chrome特征
                            '--disable-blink-features',  # 反检测特征
                            '--user-data-dir',  # 指定配置目录的进程
                            'chromedriver',  # ChromeDriver相关
                            '--no-sandbox',  # 自动化常用参数
                            '--disable-dev-shm-usage',  # 自动化常用参数
                            'linkedin_automation'  # 我们的配置目录标识
                        ]

                        if any(keyword in cmdline.lower() for keyword in automation_keywords):
                            try:
                                proc.terminate()
                                cleaned_count += 1
                                logger.debug(f"终止自动化Chrome进程: {proc.info['pid']}")
                                # 等待进程终止
                                proc.wait(timeout=3)
                            except psutil.TimeoutExpired:
                                # 强制杀死进程
                                proc.kill()
                                logger.debug(f"强制杀死Chrome进程: {proc.info['pid']}")
                        else:
                            logger.debug(f"跳过用户Chrome进程: {proc.info['pid']}")

                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            # 🔧 方法2: Windows系统使用taskkill清理残留进程
            if os.name == 'nt':  # Windows系统
                try:
                    # 清理可能的ChromeDriver进程
                    subprocess.run(['taskkill', '/f', '/im', 'chromedriver.exe'],
                                 capture_output=True, timeout=5)
                    subprocess.run(['taskkill', '/f', '/im', 'undetected_chromedriver.exe'],
                                 capture_output=True, timeout=5)
                    logger.debug("Windows系统清理ChromeDriver进程")
                except Exception as e:
                    logger.debug(f"Windows进程清理失败: {e}")

            if cleaned_count > 0:
                logger.info(f"清理了 {cleaned_count} 个自动化Chrome进程")
                time.sleep(3)  # 增加等待时间，确保进程完全终止
            else:
                logger.debug("没有发现需要清理的自动化Chrome进程")

        except ImportError:
            # 如果没有psutil，跳过清理以避免影响用户浏览器
            logger.debug("psutil不可用，跳过Chrome进程清理以保护用户浏览器")
        except Exception as e:
            logger.warning(f"清理Chrome进程时出错: {e}")

    def _ensure_clean_environment(self):
        """确保干净的Chrome环境"""
        try:
            # 🔧 清理可能的端口占用
            self._cleanup_debug_ports()

            # 🔧 清理临时文件
            self._cleanup_temp_files()

            # 短暂等待，确保清理完成
            time.sleep(1)

        except Exception as e:
            logger.debug(f"环境清理时出错: {e}")

    def _cleanup_debug_ports(self):
        """清理可能占用的调试端口"""
        try:
            import socket
            import psutil

            # 常用的Chrome调试端口范围
            debug_ports = range(9222, 9232)

            for port in debug_ports:
                try:
                    # 检查端口是否被占用
                    with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                        result = s.connect_ex(('127.0.0.1', port))
                        if result == 0:  # 端口被占用
                            # 查找占用端口的进程
                            for proc in psutil.process_iter(['pid', 'name', 'connections']):
                                try:
                                    for conn in proc.info.get('connections', []):
                                        if conn.laddr.port == port and 'chrome' in proc.info['name'].lower():
                                            proc.terminate()
                                            logger.debug(f"终止占用端口{port}的Chrome进程: {proc.info['pid']}")
                                            break
                                except (psutil.NoSuchProcess, psutil.AccessDenied):
                                    continue
                except Exception:
                    continue

        except Exception as e:
            logger.debug(f"端口清理失败: {e}")

    def _cleanup_temp_files(self):
        """清理Chrome临时文件"""
        try:
            import tempfile
            import shutil
            import glob

            temp_dir = tempfile.gettempdir()

            # 清理Chrome相关的临时文件
            patterns = [
                'chrome_*',
                'scoped_dir*',
                '.com.google.Chrome.*'
            ]

            for pattern in patterns:
                for temp_file in glob.glob(f"{temp_dir}/{pattern}"):
                    try:
                        if os.path.isdir(temp_file):
                            shutil.rmtree(temp_file, ignore_errors=True)
                        else:
                            os.remove(temp_file)
                        logger.debug(f"清理临时文件: {temp_file}")
                    except Exception:
                        continue

        except Exception as e:
            logger.debug(f"临时文件清理失败: {e}")

    def _cleanup_chrome_profile(self, profile_path: str):
        """清理可能损坏的Chrome配置文件"""
        try:
            import shutil
            from pathlib import Path

            profile_dir = Path(profile_path)
            if profile_dir.exists():
                logger.info(f"清理Chrome配置目录: {profile_path}")

                # 删除可能损坏的文件
                problematic_files = [
                    "Preferences",
                    "Local State",
                    "Secure Preferences",
                    "Default/Preferences",
                    "Default/Secure Preferences"
                ]

                for file_name in problematic_files:
                    file_path = profile_dir / file_name
                    if file_path.exists():
                        try:
                            file_path.unlink()
                            logger.debug(f"删除损坏的配置文件: {file_name}")
                        except Exception as e:
                            logger.debug(f"删除文件失败 {file_name}: {e}")

                logger.info("Chrome配置文件清理完成")
            else:
                logger.debug(f"配置目录不存在: {profile_path}")

        except Exception as e:
            logger.warning(f"清理Chrome配置文件时出错: {e}")
    
    def _apply_stealth_scripts(self):
        """应用额外的反检测脚本"""
        if not self.driver:
            return
            
        try:
            # 移除webdriver属性
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'webdriver', {
                    get: () => undefined,
                });
            """)
            
            # 修改plugins长度
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'plugins', {
                    get: () => [1, 2, 3, 4, 5],
                });
            """)
            
            # 修改语言设置
            self.driver.execute_script("""
                Object.defineProperty(navigator, 'languages', {
                    get: () => ['en-US', 'en'],
                });
            """)
            
            # 添加Chrome对象
            self.driver.execute_script("""
                window.chrome = {
                    runtime: {},
                };
            """)
            
            # 修改权限查询
            self.driver.execute_script("""
                const originalQuery = window.navigator.permissions.query;
                return window.navigator.permissions.query = (parameters) => (
                    parameters.name === 'notifications' ?
                        Promise.resolve({ state: Notification.permission }) :
                        originalQuery(parameters)
                );
            """)
            
            logger.debug("反检测脚本应用成功")
            
        except Exception as e:
            logger.warning(f"应用反检测脚本时出错: {str(e)}")
    
    def random_delay(self, min_seconds: float = 1.0, max_seconds: float = 3.0) -> float:
        """随机延迟，模拟人类行为"""
        delay = random.uniform(min_seconds, max_seconds)
        time.sleep(delay)
        logger.debug(f"随机延迟: {delay:.2f}秒")
        return delay
    
    def human_like_scroll(self, element=None):
        """人类化滚动"""
        if not self.driver:
            return
            
        try:
            if element:
                # 滚动到特定元素
                self.driver.execute_script("arguments[0].scrollIntoView({behavior: 'smooth'});", element)
            else:
                # 随机滚动
                scroll_height = self.driver.execute_script("return document.body.scrollHeight")
                current_position = self.driver.execute_script("return window.pageYOffset")
                
                # 随机滚动距离
                scroll_distance = random.randint(200, 800)
                new_position = min(current_position + scroll_distance, scroll_height)
                
                self.driver.execute_script(f"window.scrollTo({{top: {new_position}, behavior: 'smooth'}});")
                
            self.random_delay(0.5, 1.5)
            
        except Exception as e:
            logger.warning(f"人类化滚动时出错: {str(e)}")

    def force_cleanup_all(self):
        """🔧 强制清理所有Chrome相关资源 - 用于解决连接问题"""
        logger.info("🧹 开始强制清理所有Chrome相关资源...")

        try:
            # 1. 关闭当前驱动
            if self.driver:
                try:
                    self.driver.quit()
                except:
                    pass
                self.driver = None

            # 2. 清理所有Chrome进程
            self._cleanup_chrome_processes()

            # 3. 清理端口占用
            self._cleanup_debug_ports()

            # 4. 清理临时文件
            self._cleanup_temp_files()

            # 5. 等待清理完成
            time.sleep(5)

            logger.info("✅ Chrome资源清理完成，可以重新尝试创建驱动")

        except Exception as e:
            logger.warning(f"强制清理时出错: {e}")

    def quit(self):
        """安全退出驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("Undetected ChromeDriver已退出")
            except Exception as e:
                logger.warning(f"退出驱动时出错: {str(e)}")
            finally:
                self.driver = None

# 便捷函数
def create_undetected_chrome(headless: bool = False, 
                           profile_path: str = None,
                           version_main: int = None) -> 'uc.Chrome':
    """创建Undetected Chrome驱动的便捷函数"""
    manager = UndetectedChromeManager()
    return manager.create_driver(
        headless=headless,
        profile_path=profile_path,
        version_main=version_main
    )

def check_undetected_availability() -> bool:
    """检查undetected-chromedriver是否可用"""
    return UNDETECTED_AVAILABLE

def force_cleanup_chrome():
    """🔧 强制清理所有Chrome资源的便捷函数"""
    manager = UndetectedChromeManager()
    manager.force_cleanup_all()

def install_undetected_chromedriver():
    """安装undetected-chromedriver的提示"""
    if not UNDETECTED_AVAILABLE:
        print("请安装undetected-chromedriver:")
        print("pip install undetected-chromedriver")
        return False
    return True
